{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/shared/ui/buttons/textButton/index.module.css"], "sourcesContent": [".button {\n    font-size: var(--font-body-size);\n    font-weight: 600;\n    line-height: var(--font-body-line-height);\n    letter-spacing: var(--font-body-letter-spacing);\n    color: var(--surface);\n    border-radius: 1rem;\n    height: 3.25rem;\n    width: 100%;\n\n    background: var(--button-surface);\n\n    transition: background 0.2s ease;\n\n    &:hover {\n        background: var(--button-surface-hover);\n    }\n}"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;AAcI"}}]}