import { FC } from "react";
import { CountryI } from "../model/country";
import style from './index.module.css';

interface CountryCardProps {
    country: CountryI;
    onClick: () => void;
    type: 'mobile' | 'desktop';
}

const CountryCard: FC<CountryCardProps> = ({
    country,
    onClick,
    type,
}) => {
    return (
        <div onClick={onClick} className={style.card} data-type={type}>
            <span className={style.title}>{country.country}</span>
        </div>
    );
}

export default CountryCard;