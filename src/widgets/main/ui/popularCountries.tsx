import { CountryApi } from '@/src/entities/country/api'
import TextButton from '@/src/shared/ui/buttons/textButton'
import { FC } from 'react'
import style from './index.module.css'

const PopularCountries: FC = async () => {
	const countryApi = new CountryApi()
	const countries = await countryApi.fetchCountries()

	// export interface CountryI {
	// 	id?: string
	// 	country?: string
	// 	iso?: string
	// 	search?: string[]
	// 	url?: string
	// 	path?: string
	// 	classic_info?: CountryClassicInfoI
	// 	psg_info?: string
	// 	package_included?: CountryIncludedI[]
	// }

	// export interface CountryClassicInfoI {
	// 	new?: boolean
	// 	popular?: string
	// 	price_per_gb?: string
	// 	price_per_day?: string
	// }

	// export interface CountryIncludedI {
	// 	id?: string
	// }

	// export interface CountriesI {
	// 	countries?: Map<string, CountryI>;
	// }

	return (
		<div className={`${style.card} ${style.popularCountries}`}>
			<h2 className={style.title}>Популярные страны</h2>
			<div className={style.countries}>
				{countries.countries?.get('ru')?.map((country) => (
					<div key={country.id} className={style.countryItem}>
						<img
							src={country.flag}
							alt={`${country.name} flag`}
							className={style.flag}
						/>
						<span className={style.countryName}>
							{country.name}
						</span>
						<span className={style.countryCode}>
							{country.code}
						</span>
					</div>
				))}
			</div>
			<div className={style.loadMore}>
				<TextButton text='Показать все страны' />
			</div>
		</div>
	)
}

export default PopularCountries
