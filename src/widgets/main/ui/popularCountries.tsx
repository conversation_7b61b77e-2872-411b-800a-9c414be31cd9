import { CountryApi } from '@/src/entities/country/api';
import { CountriesI, CountryI } from '@/src/entities/country/model/country';
import TextButton from '@/src/shared/ui/buttons/textButton';
import { FC } from 'react';
import style from './index.module.css';

interface PopularCountriesProps {
	propsCountries?: CountriesI;
	showMore: () => void;
}

const PopularCountries: FC<PopularCountriesProps> = async ({
	propsCountries,
	showMore,
}) => {
	// for seo
	const countryApi = new CountryApi()
	const countries = await countryApi.fetchCountries()

	const countriesData = (propsCountries ?? countries).countries;
	const ruCountries: CountryI[] = countriesData?.values().next().value || [];

	return (
		<div className={`${style.card} ${style.popularCountries}`}>
			<h2 className={style.title}>Популярные страны</h2>
			<div className={style.countries}>
				{ruCountries.map((data) => (
					<div key={data.id} className={style.countryItem}>
						{/* TODO: flag */}
						<div className={style.info}>
							<span className={style.countryName}>
								{data.country}
							</span>
							<span className={style.price}>
								от ₽{data.classic_info?.price_per_gb}/GB
							</span>
						</div>
					</div>
				))}
			</div>
			<div className={style.loadMore}>
				<TextButton text='Показать все страны' onClick={showMore} />
			</div>
		</div>
	)
}

export default PopularCountries
