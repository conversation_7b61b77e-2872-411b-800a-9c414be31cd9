import { CountryApi } from '@/src/entities/country/api';
import { CountriesI, CountryI } from '@/src/entities/country/model/country';
import { FC } from 'react';
import style from './index.module.css';
import ShowMoreButton from './showMoreButton';

interface PopularCountriesProps {
	propsCountries?: CountriesI;
}

const PopularCountries: FC<PopularCountriesProps> = async ({
	propsCountries,
}) => {
	// for seo - получаем данные только если не переданы через пропсы
	let countries = propsCountries;
	if (!countries) {
		const countryApi = new CountryApi()
		countries = await countryApi.fetchCountries()
	}

	const countriesData = countries.countries;
	const ruCountries: CountryI[] = countriesData?.values().next().value || [];

	const handleShowMore = () => {
		// Логика показа всех стран
		console.log('Показать все страны')
	}

	return (
		<div className={`${style.card} ${style.popularCountries}`}>
			<h2 className={style.title}>Популярные страны</h2>
			<div className={style.countries}>
				{ruCountries.map((data) => (
					<div key={data.id} className={style.countryItem}>
						{/* TODO: flag */}
						<div className={style.info}>
							<span className={style.countryName}>
								{data.country}
							</span>
							<span className={style.price}>
								от ₽{data.classic_info?.price_per_gb}/GB
							</span>
						</div>
					</div>
				))}
			</div>
			<div className={style.loadMore}>
				<ShowMoreButton onShowMore={handleShowMore} />
			</div>
		</div>
	)
}

export default PopularCountries
