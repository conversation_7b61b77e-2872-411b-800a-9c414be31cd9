'use client'

import { CountriesI } from '@/src/entities/country/model/country'
import { useCountriesStore } from '@/src/pages/home/<USER>/countries'
import { FC, useEffect } from 'react'

interface MainContentClientProps {
	countries: CountriesI
}

const MainContentClient: FC<MainContentClientProps> = ({ countries }) => {
	const { setCountries } = useCountriesStore()

	// Устанавливаем данные в store для клиентской части
	useEffect(() => {
		setCountries(countries)
	}, [countries, setCountries])

	// Этот компонент не рендерит ничего, только управляет состоянием
	return null
}

export default MainContentClient
