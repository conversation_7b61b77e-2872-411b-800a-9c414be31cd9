import { CountryApi } from '@/src/entities/country/api'
import { FC } from 'react'
import HowItsWork from '../../howItWorks/ui'
import style from './index.module.css'
import MainContentClient from './mainContentClient'
import PopularCountries from './popularCountries'

const MainContent: FC = async () => {
    // Получаем данные на сервере для SSR
    const countryApi = new CountryApi()
    const countries = await countryApi.fetchCountries()

    return (
        <main className={style.main}>
            <PopularCountries propsCountries={countries} />
            <MainContentClient countries={countries} />
            <HowItsWork />
        </main>
    )
}

export default MainContent
