'use client'

import { CountryApi } from '@/src/entities/country/api'
import { useCountriesStore } from '@/src/pages/home/<USER>/countries'
import { FC, useEffect } from 'react'
import HowItsWork from '../../howItWorks/ui'
import style from './index.module.css'
import PopularCountries from './popularCountries'

const MainContent: FC = async () => {
    const { countries, setCountries } = useCountriesStore()

    const fetchCountries = async () => {
        const countryApi = new CountryApi()
        const countries = await countryApi.fetchCountries()
        setCountries(countries)
    }

    useEffect(() => {
        fetchCountries()
    }, [])

    const showMore = () => {
        console.log('show more')
    }


    return (
        <main className={style.main}>
            <PopularCountries propsCountries={countries} showMore={showMore} />
            <HowItsWork />
        </main>
    )
}

export default MainContent
