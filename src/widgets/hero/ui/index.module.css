.hero {
	padding: 1.25rem 0 1rem 0;
	display: flex;
	flex-direction: column;
	gap: 2rem;
}

.title {
	font-size: var(--font-title-size);
	font-weight: var(--font-title-weight);
	line-height: var(--font-title-line-height);
	letter-spacing: var(--font-title-letter-spacing);
	text-align: center;
	text-wrap: balance;
}

.search {
	position: relative;
}

.dropdown {
	&[data-is-visible='false'] {
		opacity: 0;
		pointer-events: none;
	}

	transition: opacity 0.2s ease;
	opacity: 1;
	position: absolute;
	top: calc(100% + 0.375rem);
	left: 0;
	width: 100%;
	background: var(--surface);
	min-height: 8.125rem;
	max-height: 18.75rem;

	box-shadow: 0 0.5rem 0.625rem 0.25rem rgba(0, 0, 0, 0.1);

	display: flex;
	flex-direction: column;
	border-radius: 1.25rem;
}

.notFound {
	margin: auto;
}